'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { Menu, ChevronDown, Lightbulb, MessageCircle, LogIn } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Examples } from './suggestions/examples';

export function UnauthenticatedDashboard() {
  const [inputValue, setInputValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const router = useRouter();
  const isMobile = useIsMobile();
  const { setOpenMobile } = useSidebar();

  const handleAuthRedirect = () => {
    setIsRedirecting(true);
    // Redirect to auth page with return URL to dashboard
    router.push('/auth?returnUrl=' + encodeURIComponent('/dashboard'));
  };

  const handleInputClick = () => {
    handleAuthRedirect();
  };

  const handleExampleClick = (prompt: string) => {
    // Store the selected prompt and redirect to auth
    localStorage.setItem('pendingAgentPrompt', prompt);
    handleAuthRedirect();
  };

  return (
    <div className="flex flex-col min-h-screen w-full">
      {isMobile && (
        <div className="absolute top-4 left-4 z-10">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setOpenMobile(true)}
              >
                <Menu className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>Open menu</TooltipContent>
          </Tooltip>
        </div>
      )}

      {/* Main Content Container */}
      <div className="flex-1 flex flex-col items-center justify-center px-4 py-8">
        {/* Chat Input Section */}
        <div className="w-[650px] max-w-[90%] mb-8">
          <div className="flex flex-col items-center text-center w-full mb-0">
            <h1 className="tracking-tight text-4xl text-muted-foreground leading-tight">
              Hey
            </h1>
            <p className="tracking-tight text-3xl font-normal text-muted-foreground/80 mt-2">
              What would you like to do today?
            </p>
          </div>

          <div className={cn(
            "w-full",
            "max-w-full",
            "sm:max-w-3xl"
          )}>
            {/* Disabled Chat Input Preview */}
            <div className="w-full text-sm flex flex-col justify-between items-start rounded-lg">
              <div className="w-full p-1.5 pb-2 bg-sidebar rounded-2xl border relative">
                <div
                  className="relative cursor-pointer"
                  onClick={handleInputClick}
                >
                  <textarea
                    className="w-full bg-transparent border-0 resize-none outline-none placeholder:text-muted-foreground/60 text-foreground p-3 pr-12 min-h-[48px] max-h-[200px] cursor-pointer"
                    placeholder="Describe what you need help with..."
                    value={inputValue}
                    readOnly
                    rows={1}
                  />

                  {/* Overlay to capture clicks */}
                  <div className="absolute inset-0 bg-transparent cursor-pointer" />

                  {/* Send button area */}
                  <div className="absolute bottom-2 right-2 flex items-center gap-2">
                    <Button
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={handleInputClick}
                    >
                      <LogIn className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Auth prompt overlay */}
                <div className="absolute inset-0 bg-background/80 backdrop-blur-sm rounded-2xl flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
                  <div className="text-center">
                    {isRedirecting ? (
                      <>
                        <div className="h-6 w-6 mx-auto mb-2 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                        <p className="text-sm font-medium text-foreground">Redirecting to sign in...</p>
                      </>
                    ) : (
                      <>
                        <LogIn className="h-6 w-6 mx-auto mb-2 text-primary" />
                        <p className="text-sm font-medium text-foreground">Sign in to start chatting</p>
                        <p className="text-xs text-muted-foreground">Click anywhere to continue</p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Suggestions Section - Full Width, Below Chat Input */}
        <AnimatePresence>
          {showSuggestions && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="w-full overflow-hidden px-8 pb-4"
            >
              <Examples onSelectPrompt={handleExampleClick} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Floating Buttons - Bottom Right */}
      <div className="fixed bottom-6 right-6 z-50 flex items-center gap-2">
        {/* Preview Usage Indicator */}
        <div className="flex items-center gap-1.5 px-2 py-1 h-7 text-xs text-muted-foreground bg-muted rounded-lg shadow-lg">
          <MessageCircle className="h-3 w-3" />
          <span className="text-xs select-none font-medium">
            Sign in to see usage
          </span>
        </div>

        {/* Suggestions Toggle Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowSuggestions(!showSuggestions)}
          className="flex items-center gap-1 px-2 py-1 h-7 text-xs text-muted-foreground bg-muted hover:text-accent-foreground hover:bg-muted/80 rounded-lg shadow-lg"
        >
          <Lightbulb className="h-3 w-3" />
          <span className="text-xs select-none">
            {showSuggestions ? 'Hide' : 'Ideas'}
          </span>
          <motion.div
            animate={{ rotate: showSuggestions ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="h-3 w-3" />
          </motion.div>
        </Button>

        {/* Sign In Button */}
        <Button
          onClick={handleAuthRedirect}
          size="sm"
          disabled={isRedirecting}
          className="flex items-center gap-1 px-3 py-1 h-7 text-xs rounded-lg shadow-lg"
        >
          {isRedirecting ? (
            <>
              <div className="h-3 w-3 animate-spin rounded-full border border-current border-t-transparent" />
              <span className="text-xs select-none font-medium">
                Redirecting...
              </span>
            </>
          ) : (
            <>
              <LogIn className="h-3 w-3" />
              <span className="text-xs select-none font-medium">
                Sign In
              </span>
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
